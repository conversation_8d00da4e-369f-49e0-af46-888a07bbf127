import 'package:flutter/material.dart';
import '../models/job_posting.dart';
import '../services/job_data_service.dart';
import 'mobile_job_card.dart';

/// Mobile-optimized job directory with pull-to-refresh and collapsible filters
class MobileJobDirectory extends StatefulWidget {
  final bool showSearchBar;
  final bool showFilters;
  final int? maxItems;
  final String? title;
  final bool isCompact;

  const MobileJobDirectory({
    super.key,
    this.showSearchBar = true,
    this.showFilters = true,
    this.maxItems,
    this.title,
    this.isCompact = false,
  });

  @override
  State<MobileJobDirectory> createState() => _MobileJobDirectoryState();
}

class _MobileJobDirectoryState extends State<MobileJobDirectory> {
  List<JobPosting> _allJobs = [];
  List<JobPosting> _filteredJobs = [];
  List<JobPosting> _displayedJobs = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String _searchQuery = '';
  String _selectedLocation = 'All Locations';
  String _selectedOrganization = 'All Organizations';
  String _selectedCategory = 'All Categories';
  String _sortBy = 'Recent';
  bool _filtersExpanded = false;

  // Pagination
  int _currentPage = 1;
  final int _jobsPerPage = 15;
  int _totalPages = 1;

  List<String> _locations = ['All Locations'];
  List<String> _organizations = ['All Organizations'];
  List<String> _categories = ['All Categories'];

  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadJobs();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadJobs() async {
    try {
      final jobs = await JobDataService.loadJobPostings();
      setState(() {
        _allJobs = jobs;
        _filteredJobs = jobs;
        _isLoading = false;

        _locations = ['All Locations', ...JobDataService.getUniqueLocations(jobs)];
        _organizations = ['All Organizations', ...JobDataService.getUniqueOrganizations(jobs)];
        _categories = ['All Categories', ...JobDataService.getUniqueCategories(jobs)];
      });
      _applyFilters();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading jobs: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _applyFilters() {
    List<JobPosting> filtered = List.from(_allJobs);

    if (_searchQuery.isNotEmpty) {
      filtered = JobDataService.filterJobs(filtered, _searchQuery);
    }

    if (_selectedLocation != 'All Locations') {
      filtered = filtered.where((job) => job.formattedLocation == _selectedLocation).toList();
    }

    if (_selectedOrganization != 'All Organizations') {
      filtered = filtered.where((job) => job.organization == _selectedOrganization).toList();
    }

    if (_selectedCategory != 'All Categories') {
      filtered = filtered.where((job) => job.category == _selectedCategory).toList();
    }

    switch (_sortBy) {
      case 'Recent':
        break;
      case 'Closing Date':
        filtered = JobDataService.sortJobsByClosingDate(filtered);
        break;
      case 'Company A-Z':
        filtered.sort((a, b) => a.organization.compareTo(b.organization));
        break;
      case 'Location':
        filtered.sort((a, b) => a.formattedLocation.compareTo(b.formattedLocation));
        break;
    }

    setState(() {
      _filteredJobs = filtered;
      _currentPage = 1; // Reset to first page when filters change
      _updateDisplayedJobs();
    });
  }

  void _updateDisplayedJobs() {
    // Calculate total pages
    int totalJobs = _filteredJobs.length;
    if (widget.maxItems != null && totalJobs > widget.maxItems!) {
      totalJobs = widget.maxItems!;
      _filteredJobs = _filteredJobs.take(widget.maxItems!).toList();
    }

    _totalPages = (totalJobs / _jobsPerPage).ceil();
    if (_totalPages == 0) _totalPages = 1;

    // Calculate displayed jobs for current page
    int startIndex = (_currentPage - 1) * _jobsPerPage;
    int endIndex = startIndex + _jobsPerPage;

    if (endIndex > _filteredJobs.length) {
      endIndex = _filteredJobs.length;
    }

    _displayedJobs = _filteredJobs.sublist(startIndex, endIndex);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search bar
        if (widget.showSearchBar) _buildMobileSearchBar(),
        
        // Filters toggle and content
        if (widget.showFilters) _buildFiltersSection(),
        
        // Job count
        _buildJobCount(),
        
        // Job list
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredJobs.isEmpty
                  ? _buildEmptyState()
                  : _buildJobList(),
        ),
      ],
    );
  }

  Widget _buildMobileSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search jobs...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                    _applyFilters();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.grey[100],
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
          _applyFilters();
        },
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Column(
      children: [
        // Filter toggle button
        InkWell(
          onTap: () {
            setState(() {
              _filtersExpanded = !_filtersExpanded;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.filter_list, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Text(
                      'Filters',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
                Icon(
                  _filtersExpanded ? Icons.expand_less : Icons.expand_more,
                  color: Colors.grey[600],
                ),
              ],
            ),
          ),
        ),
        
        // Expandable filters
        if (_filtersExpanded) _buildExpandedFilters(),
      ],
    );
  }

  Widget _buildExpandedFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Sort dropdown
          Row(
            children: [
              const Icon(Icons.sort, size: 20),
              const SizedBox(width: 8),
              const Text('Sort by: '),
              Expanded(
                child: DropdownButton<String>(
                  value: _sortBy,
                  isExpanded: true,
                  underline: Container(),
                  items: ['Recent', 'Closing Date', 'Company A-Z', 'Location']
                      .map((sort) => DropdownMenuItem(
                            value: sort,
                            child: Text(sort),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                    _applyFilters();
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Location filter
          Row(
            children: [
              const Icon(Icons.location_on, size: 20),
              const SizedBox(width: 8),
              const Text('Location: '),
              Expanded(
                child: DropdownButton<String>(
                  value: _selectedLocation,
                  isExpanded: true,
                  underline: Container(),
                  items: _locations.map((location) {
                    return DropdownMenuItem(
                      value: location,
                      child: Text(
                        location,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 14),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedLocation = value!;
                    });
                    _applyFilters();
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),

          // Organization filter
          Row(
            children: [
              const Icon(Icons.business, size: 20),
              const SizedBox(width: 8),
              const Text('Company: '),
              Expanded(
                child: DropdownButton<String>(
                  value: _selectedOrganization,
                  isExpanded: true,
                  underline: Container(),
                  items: _organizations.map((org) {
                    return DropdownMenuItem(
                      value: org,
                      child: Text(
                        org,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 14),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedOrganization = value!;
                    });
                    _applyFilters();
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Category filter
          Row(
            children: [
              const Icon(Icons.category, size: 20),
              const SizedBox(width: 8),
              const Text('Category: '),
              Expanded(
                child: DropdownButton<String>(
                  value: _selectedCategory,
                  isExpanded: true,
                  underline: Container(),
                  items: _categories.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(
                        category,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 14),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value!;
                    });
                    _applyFilters();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildJobCount() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${_filteredJobs.length} jobs found',
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
              ),
              if (_displayedJobs.isNotEmpty)
                Text(
                  'Page $_currentPage of $_totalPages',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
            ],
          ),
          if (_displayedJobs.isNotEmpty && _totalPages > 1)
            const SizedBox(height: 8),
          if (_displayedJobs.isNotEmpty && _totalPages > 1)
            _buildPaginationControls(),
        ],
      ),
    );
  }

  Widget _buildPaginationControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Previous page button
        IconButton(
          onPressed: _currentPage > 1 ? () => _goToPage(_currentPage - 1) : null,
          icon: const Icon(Icons.chevron_left),
          style: IconButton.styleFrom(
            backgroundColor: _currentPage > 1 ? Colors.blue[50] : Colors.grey[100],
          ),
        ),

        const SizedBox(width: 8),

        // Page numbers (show current and nearby pages)
        ..._buildPageNumbers(),

        const SizedBox(width: 8),

        // Next page button
        IconButton(
          onPressed: _currentPage < _totalPages ? () => _goToPage(_currentPage + 1) : null,
          icon: const Icon(Icons.chevron_right),
          style: IconButton.styleFrom(
            backgroundColor: _currentPage < _totalPages ? Colors.blue[50] : Colors.grey[100],
          ),
        ),
      ],
    );
  }

  List<Widget> _buildPageNumbers() {
    List<Widget> pageNumbers = [];

    // Show first page if not near it
    if (_currentPage > 3) {
      pageNumbers.add(_buildPageButton(1));
      if (_currentPage > 4) {
        pageNumbers.add(const Text('...', style: TextStyle(color: Colors.grey)));
      }
    }

    // Show pages around current page
    int start = (_currentPage - 2).clamp(1, _totalPages);
    int end = (_currentPage + 2).clamp(1, _totalPages);

    for (int i = start; i <= end; i++) {
      pageNumbers.add(_buildPageButton(i));
    }

    // Show last page if not near it
    if (_currentPage < _totalPages - 2) {
      if (_currentPage < _totalPages - 3) {
        pageNumbers.add(const Text('...', style: TextStyle(color: Colors.grey)));
      }
      pageNumbers.add(_buildPageButton(_totalPages));
    }

    return pageNumbers;
  }

  Widget _buildPageButton(int pageNumber) {
    bool isCurrentPage = pageNumber == _currentPage;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2),
      child: InkWell(
        onTap: () => _goToPage(pageNumber),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: isCurrentPage ? Colors.blue : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isCurrentPage ? Colors.blue : Colors.grey[300]!,
            ),
          ),
          child: Text(
            pageNumber.toString(),
            style: TextStyle(
              color: isCurrentPage ? Colors.white : Colors.black,
              fontWeight: isCurrentPage ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  void _goToPage(int page) {
    if (page >= 1 && page <= _totalPages && page != _currentPage) {
      setState(() {
        _currentPage = page;
        _updateDisplayedJobs();
      });

      // Scroll to top when changing pages
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Widget _buildJobList() {
    return RefreshIndicator(
      onRefresh: _loadJobs,
      child: ListView.builder(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: _displayedJobs.length,
        itemBuilder: (context, index) {
          return MobileJobCard(
            job: _displayedJobs[index],
            isCompact: widget.isCompact,
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.work_off,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No jobs found',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or filters',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                _searchController.clear();
                setState(() {
                  _searchQuery = '';
                  _selectedLocation = 'All Locations';
                  _selectedOrganization = 'All Organizations';
                  _selectedCategory = 'All Categories';
                  _sortBy = 'Recent';
                });
                _applyFilters();
              },
              child: const Text('Clear Filters'),
            ),
          ],
        ),
      ),
    );
  }
}
